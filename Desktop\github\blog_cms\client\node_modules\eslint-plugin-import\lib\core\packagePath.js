'use strict';Object.defineProperty(exports, "__esModule", { value: true });exports.




getFilePackagePath = getFilePackagePath;exports.




getContextPackagePath = getContextPackagePath;exports.



getFilePackageName = getFilePackageName;var _path = require('path');var _contextCompat = require('eslint-module-utils/contextCompat');var _pkgUp = require('eslint-module-utils/pkgUp');var _pkgUp2 = _interopRequireDefault(_pkgUp);var _readPkgUp2 = require('eslint-module-utils/readPkgUp');var _readPkgUp3 = _interopRequireDefault(_readPkgUp2);function _interopRequireDefault(obj) {return obj && obj.__esModule ? obj : { 'default': obj };}function getFilePackagePath(filePath) {var fp = (0, _pkgUp2['default'])({ cwd: filePath });return (0, _path.dirname)(fp);}function getContextPackagePath(context) {return getFilePackagePath((0, _contextCompat.getPhysicalFilename)(context));}function getFilePackageName(filePath) {var _readPkgUp =
  (0, _readPkgUp3['default'])({ cwd: filePath, normalize: false }),pkg = _readPkgUp.pkg,path = _readPkgUp.path;
  if (pkg) {
    // recursion in case of intermediate esm package.json without name found
    return pkg.name || getFilePackageName((0, _path.dirname)((0, _path.dirname)(path)));
  }
  return null;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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